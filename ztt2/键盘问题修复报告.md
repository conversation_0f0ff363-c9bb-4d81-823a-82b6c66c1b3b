# 键盘立即缩回问题修复报告

## 问题描述

我是Claude Sonnet 4模型。在iPhone真机测试中发现，在成员详情页中点击抽奖，选择抽奖道具，点击"前往设置"按钮，弹出的道具配置弹窗中，点击选择奖品设置的输入框时，弹出键盘后，键盘会立即缩回。

## 问题根源分析

通过详细的日志分析，发现问题的根本原因是：

### 🔍 NotificationCenter观察者重复注册导致的连锁反应

1. **观察者风暴**：
   - 键盘显示通知被触发了20次
   - 键盘隐藏通知也被触发了20次
   - 多个KeyboardToolbarModifier实例同时监听同一个键盘事件

2. **观察者生命周期管理缺失**：
   - 每次onAppear都注册新的观察者
   - onDisappear时没有移除观察者
   - 导致观察者累积，造成状态混乱

3. **视图重建循环**：
   - 频繁的状态变化导致SwiftUI重建视图
   - TextField的onDisappear和onAppear被频繁调用
   - 进一步加剧了问题

## 修复方案

### 1. 添加观察者状态管理

在`KeyboardToolbarModifier`中添加了观察者引用：

```swift
struct KeyboardToolbarModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    @State private var keyboardShowObserver: NSObjectProtocol?
    @State private var keyboardHideObserver: NSObjectProtocol?
    // ...
}
```

### 2. 实现正确的观察者生命周期

```swift
private func setupKeyboardObservers() {
    // 先移除可能存在的观察者
    removeKeyboardObservers()
    
    // 注册新的观察者并保存引用
    keyboardShowObserver = NotificationCenter.default.addObserver(...)
    keyboardHideObserver = NotificationCenter.default.addObserver(...)
}

private func removeKeyboardObservers() {
    // 正确移除观察者
    if let observer = keyboardShowObserver {
        NotificationCenter.default.removeObserver(observer)
        keyboardShowObserver = nil
    }
    // ...
}
```

### 3. 在视图生命周期中正确调用

```swift
.onAppear {
    setupKeyboardObservers()
}
.onDisappear {
    removeKeyboardObservers()
}
```

## 修复的文件

- `ztt2/Views/Components/KeyboardToolbarView.swift`
  - 添加了观察者状态管理
  - 实现了正确的观察者生命周期
  - 添加了详细的调试日志

## 预期效果

修复后应该看到：
1. 键盘显示/隐藏通知只触发一次
2. TextField能够正常获得焦点
3. 键盘保持稳定显示
4. 观察者正确创建和销毁

## 测试建议

请在iPhone真机上重新测试：
1. 打开成员详情页
2. 点击抽奖 → 选择盲盒 → 点击"前往设置"
3. 点击任意奖品输入框
4. 观察键盘是否能正常显示并保持稳定
5. 查看控制台日志，确认观察者管理正常

## 技术要点

- **观察者模式的正确使用**：确保观察者的创建和销毁配对
- **SwiftUI生命周期管理**：正确使用onAppear/onDisappear
- **内存泄漏防护**：避免观察者累积导致的内存问题
- **状态管理**：使用@State正确管理观察者引用

这个修复解决了键盘工具栏功能中的核心架构问题，应该能彻底解决键盘立即缩回的问题。
