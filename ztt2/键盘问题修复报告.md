# 键盘立即缩回问题修复报告

## 问题描述

我是Claude Sonnet 4模型。在iPhone真机测试中发现，在成员详情页中点击抽奖，选择抽奖道具，点击"前往设置"按钮，弹出的道具配置弹窗中，点击选择奖品设置的输入框时，弹出键盘后，键盘会立即缩回。

## 问题分析

### 根本原因

通过详细的日志分析，发现问题的根本原因是：**NotificationCenter观察者重复注册导致的连锁反应**

### 具体表现

1. **键盘显示通知被触发了20次**：
   ```
   🔍 [KeyboardToolbar] 键盘即将显示通知
   🔍 [KeyboardToolbar] 键盘高度: 368.0
   🔍 [KeyboardToolbar] 键盘工具栏状态更新完成
   ```
   这个模式重复了20次！

2. **紧接着键盘隐藏通知也被触发了20次**：
   ```
   🔍 [KeyboardToolbar] 键盘即将隐藏通知
   🔍 [KeyboardToolbar] 键盘工具栏隐藏完成
   ```

3. **TextField的onDisappear和onAppear被频繁调用**

### 技术原因

**多个KeyboardToolbarModifier实例同时监听键盘事件**，每个实例都：
1. 注册了键盘显示/隐藏的观察者
2. 当键盘显示时，所有观察者都被触发
3. 每个观察者都尝试更新UI状态
4. 这导致了视图的频繁重建和键盘状态的混乱

## 解决方案

### 修复内容

修复了 `KeyboardToolbarModifier` 中的NotificationCenter观察者管理问题：

#### 修复前的问题代码：
```swift
struct KeyboardToolbarModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    
    func body(content: Content) -> some View {
        // ...
        .onAppear {
            // 直接注册观察者，没有管理生命周期
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                // ...
            }
            
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                // ...
            }
        }
        // 没有onDisappear来移除观察者
    }
}
```

#### 修复后的正确代码：
```swift
struct KeyboardToolbarModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    @State private var keyboardShowObserver: NSObjectProtocol?
    @State private var keyboardHideObserver: NSObjectProtocol?
    
    func body(content: Content) -> some View {
        // ...
        .onAppear {
            setupKeyboardObservers()
        }
        .onDisappear {
            removeKeyboardObservers()
        }
    }
    
    private func setupKeyboardObservers() {
        // 先移除可能存在的观察者
        removeKeyboardObservers()
        
        // 正确注册观察者并保存引用
        keyboardShowObserver = NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillShowNotification,
            object: nil,
            queue: .main
        ) { notification in
            // ...
        }
        
        keyboardHideObserver = NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillHideNotification,
            object: nil,
            queue: .main
        ) { _ in
            // ...
        }
    }
    
    private func removeKeyboardObservers() {
        if let observer = keyboardShowObserver {
            NotificationCenter.default.removeObserver(observer)
            keyboardShowObserver = nil
        }
        
        if let observer = keyboardHideObserver {
            NotificationCenter.default.removeObserver(observer)
            keyboardHideObserver = nil
        }
    }
}
```

### 关键改进

1. **添加观察者引用管理**：使用 `@State` 变量保存观察者引用
2. **正确的生命周期管理**：在 `onAppear` 时注册，在 `onDisappear` 时移除
3. **防止重复注册**：在注册新观察者前先移除可能存在的观察者
4. **内存泄漏防护**：确保观察者在视图销毁时被正确移除

## 修复验证

### 编译状态
✅ **编译成功** - 所有修改都通过了编译验证

### 预期效果
修复后，应该能够观察到：
1. 键盘显示/隐藏通知只被触发一次
2. TextField能够正常获得焦点并保持键盘显示
3. 键盘工具栏正常工作，不会干扰输入框的焦点

## 测试建议

请在iPhone真机上重新测试以下流程：
1. 打开成员详情页
2. 点击抽奖按钮
3. 选择盲盒
4. 点击"前往设置"按钮
5. 在弹出的盲盒配置弹窗中，点击任意一个奖品输入框
6. 观察键盘是否能正常显示并保持稳定

## 技术总结

这个问题是一个典型的SwiftUI中NotificationCenter观察者管理不当导致的问题。在复杂的视图层级中，如果不正确管理观察者的生命周期，很容易导致：

1. **内存泄漏**：观察者没有被正确移除
2. **重复注册**：同一个观察者被多次注册
3. **状态混乱**：多个观察者同时响应同一个事件
4. **UI异常**：频繁的状态更新导致视图重建和交互异常

通过正确的观察者生命周期管理，这类问题可以得到根本性的解决。
