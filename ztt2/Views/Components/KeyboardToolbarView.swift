import SwiftUI

/**
 * 键盘工具栏视图
 * 提供在键盘右上角显示一个收起键盘按钮的功能
 */
struct KeyboardToolbarView: View {
    var body: some View {
        Button(action: {
            print("🔍 [KeyboardToolbar] 收回键盘按钮被点击")
            UIApplication.shared.hideKeyboard()
        }) {
            Image("jianpan")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 30, height: 30)
                .foregroundColor(.primary)
                .padding(8)
        }
        .background(Color(.systemGray5).opacity(0.8))
        .cornerRadius(8)
        .onAppear {
            print("🔍 [KeyboardToolbar] 键盘工具栏按钮出现")
        }
        .onDisappear {
            print("🔍 [KeyboardToolbar] 键盘工具栏按钮消失")
        }
    }
}

/**
 * 键盘工具栏修饰符
 * 为任何视图添加键盘工具栏功能
 */
struct KeyboardToolbarModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    @State private var keyboardShowObserver: NSObjectProtocol?
    @State private var keyboardHideObserver: NSObjectProtocol?

    func body(content: Content) -> some View {
        ZStack {
            content

            if keyboardHeight > 0 {
                GeometryReader { geometry in
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            KeyboardToolbarView()
                                .padding(.trailing, 10)
                                .padding(.bottom, 5)
                        }
                    }
                    .frame(height: geometry.size.height)
                    .offset(y: -keyboardHeight)
                }
                .ignoresSafeArea()
            }
        }
        .onAppear {
            setupKeyboardObservers()
        }
        .onDisappear {
            removeKeyboardObservers()
        }
    }

    private func setupKeyboardObservers() {
        print("🔍 [KeyboardToolbar] 设置键盘观察者")

        // 先移除可能存在的观察者
        removeKeyboardObservers()

        // 监听键盘显示事件
        keyboardShowObserver = NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillShowNotification,
            object: nil,
            queue: .main
        ) { notification in
            print("🔍 [KeyboardToolbar] 键盘即将显示通知")
            if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
                print("🔍 [KeyboardToolbar] 键盘高度: \(keyboardFrame.height)")
                withAnimation(.easeOut(duration: 0.25)) {
                    keyboardHeight = keyboardFrame.height
                }
                print("🔍 [KeyboardToolbar] 键盘工具栏状态更新完成")
            } else {
                print("🔍 [KeyboardToolbar] ⚠️ 无法获取键盘高度信息")
            }
        }

        // 监听键盘隐藏事件
        keyboardHideObserver = NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillHideNotification,
            object: nil,
            queue: .main
        ) { _ in
            print("🔍 [KeyboardToolbar] 键盘即将隐藏通知")
            withAnimation(.easeOut(duration: 0.25)) {
                keyboardHeight = 0
            }
            print("🔍 [KeyboardToolbar] 键盘工具栏隐藏完成")
        }
    }

    private func removeKeyboardObservers() {
        print("🔍 [KeyboardToolbar] 移除键盘观察者")

        if let observer = keyboardShowObserver {
            NotificationCenter.default.removeObserver(observer)
            keyboardShowObserver = nil
        }

        if let observer = keyboardHideObserver {
            NotificationCenter.default.removeObserver(observer)
            keyboardHideObserver = nil
        }
    }
}

/**
 * 添加键盘工具栏功能的扩展
 */
extension View {
    func keyboardToolbar() -> some View {
        modifier(KeyboardToolbarModifier())
    }
}
