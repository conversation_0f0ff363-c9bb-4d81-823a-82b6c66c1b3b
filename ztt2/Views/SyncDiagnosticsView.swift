//
//  SyncDiagnosticsView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 同步诊断视图
 * 用于诊断和解决CloudKit同步问题
 */
struct SyncDiagnosticsView: View {
    @StateObject private var syncManager = iCloudSyncManager.shared
    @State private var diagnosticResults: [String] = []
    @State private var isRunningDiagnostics = false
    @State private var showResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(spacing: 10) {
                    Image(systemName: "stethoscope")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                    
                    Text("同步诊断工具")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("检查成长日记、AI分析报告和成员数据的同步状态")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // 当前同步状态
                VStack(alignment: .leading, spacing: 10) {
                    HStack {
                        Image(systemName: syncManager.syncStatus.iconName)
                            .foregroundColor(syncStatusColor)
                        Text("当前状态: \(syncManager.syncStatus.displayText)")
                            .fontWeight(.medium)
                    }
                    
                    if let lastSync = syncManager.lastSyncDate {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.secondary)
                            Text("上次同步: \(formatDate(lastSync))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if let error = syncManager.errorMessage {
                        HStack {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.red)
                            Text("错误: \(error)")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                
                // 操作按钮
                VStack(spacing: 15) {
                    Button(action: runDiagnostics) {
                        HStack {
                            if isRunningDiagnostics {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "magnifyingglass")
                            }
                            Text(isRunningDiagnostics ? "诊断中..." : "开始诊断")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunningDiagnostics)
                    
                    Button(action: forceSyncData) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("强制同步")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunningDiagnostics)

                    Button(action: forceRefreshData) {
                        HStack {
                            Image(systemName: "arrow.down.circle")
                            Text("刷新本地数据")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunningDiagnostics)
                }
                
                // 诊断结果
                if showResults && !diagnosticResults.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("诊断结果")
                            .font(.headline)
                            .padding(.bottom, 5)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 8) {
                                ForEach(Array(diagnosticResults.enumerated()), id: \.offset) { index, result in
                                    HStack(alignment: .top) {
                                        Text("\(index + 1).")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                            .frame(width: 20, alignment: .leading)
                                        
                                        Text(result)
                                            .font(.caption)
                                            .multilineTextAlignment(.leading)
                                        
                                        Spacer()
                                    }
                                    .padding(.vertical, 2)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("同步诊断")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 计算属性
    
    private var syncStatusColor: Color {
        switch syncManager.syncStatus {
        case .idle:
            return .gray
        case .syncing, .migrating:
            return .blue
        case .success:
            return .green
        case .failed:
            return .red
        }
    }
    
    // MARK: - 方法
    
    private func runDiagnostics() {
        isRunningDiagnostics = true
        showResults = false
        
        Task {
            let results = await syncManager.diagnoseDiarySyncIssues()
            
            await MainActor.run {
                self.diagnosticResults = results
                self.isRunningDiagnostics = false
                self.showResults = true
            }
        }
    }
    
    private func forceSyncData() {
        Task {
            await syncManager.triggerManualSync()
        }
    }

    private func forceRefreshData() {
        Task {
            await syncManager.forceRefreshLocalData()
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Preview

#Preview {
    SyncDiagnosticsView()
}
